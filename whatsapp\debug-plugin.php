<?php
/**
 * Debug script to identify WhatsApp Order Button plugin issues
 */

echo "=== WhatsApp Order Button Debug Script ===\n";
echo "PHP Version: " . PHP_VERSION . "\n";

// Check if files exist
$files_to_check = [
    'whatsapp-order-button.php',
    'includes/class-whatsapp-order-button.php',
    'includes/class-whatsapp-admin.php',
    'includes/class-whatsapp-frontend.php',
    'admin/admin-settings.php',
    'public/css/whatsapp-order.css',
    'public/js/whatsapp-order.js',
    'admin/css/admin.css',
    'admin/js/admin.js'
];

echo "\n=== File Existence Check ===\n";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✓ $file exists\n";
    } else {
        echo "✗ $file MISSING\n";
    }
}

// Check for syntax errors in PHP files
echo "\n=== PHP Syntax Check ===\n";
$php_files = [
    'whatsapp-order-button.php',
    'includes/class-whatsapp-order-button.php',
    'includes/class-whatsapp-admin.php',
    'includes/class-whatsapp-frontend.php',
    'admin/admin-settings.php'
];

foreach ($php_files as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_code = 0;
        exec("php -l \"$file\" 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "✓ $file - No syntax errors\n";
        } else {
            echo "✗ $file - SYNTAX ERROR:\n";
            foreach ($output as $line) {
                echo "  $line\n";
            }
        }
    }
}

// Check for common issues
echo "\n=== Common Issues Check ===\n";

// Check for null coalescing operator usage
$files_with_null_coalescing = [];
foreach ($php_files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, '??') !== false) {
            $files_with_null_coalescing[] = $file;
        }
    }
}

if (!empty($files_with_null_coalescing)) {
    echo "⚠ Files still using null coalescing operator (??) - may cause issues on PHP < 7.0:\n";
    foreach ($files_with_null_coalescing as $file) {
        echo "  - $file\n";
    }
} else {
    echo "✓ No null coalescing operators found\n";
}

// Check for missing WordPress functions
echo "\n=== WordPress Function Dependencies ===\n";
$wp_functions = [
    'add_action', 'add_filter', 'wp_enqueue_script', 'wp_enqueue_style',
    'admin_url', 'plugin_dir_path', 'plugin_dir_url', 'plugin_basename',
    '__', '_e', 'esc_attr', 'esc_html', 'wp_verify_nonce', 'wp_create_nonce'
];

foreach ($wp_functions as $func) {
    if (function_exists($func)) {
        echo "✓ $func available\n";
    } else {
        echo "✗ $func NOT AVAILABLE (WordPress not loaded)\n";
    }
}

echo "\n=== Debug Complete ===\n";
echo "If syntax errors were found, fix them first.\n";
echo "If WordPress functions are not available, this script is running outside WordPress.\n";
echo "The actual error might be visible in WordPress error logs or by enabling WP_DEBUG.\n";
