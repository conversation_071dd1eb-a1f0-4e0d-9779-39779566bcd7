<?php
/**
 * WordPress Debug Helper for WhatsApp Order Button
 * 
 * Place this file in your WordPress plugins directory and activate it
 * to help debug the WhatsApp Order Button plugin issues.
 */

/*
Plugin Name: WhatsApp Order Button Debug Helper
Description: Debug helper for WhatsApp Order Button plugin
Version: 1.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

add_action('admin_notices', 'whatsapp_debug_admin_notice');
add_action('admin_menu', 'whatsapp_debug_admin_menu');

function whatsapp_debug_admin_notice() {
    echo '<div class="notice notice-info"><p>';
    echo 'WhatsApp Order Button Debug Helper is active. Check the debug page in Tools menu.';
    echo '</p></div>';
}

function whatsapp_debug_admin_menu() {
    add_management_page(
        'WhatsApp Debug',
        'WhatsApp Debug',
        'manage_options',
        'whatsapp-debug',
        'whatsapp_debug_page'
    );
}

function whatsapp_debug_page() {
    ?>
    <div class="wrap">
        <h1>WhatsApp Order Button Debug Information</h1>
        
        <h2>System Information</h2>
        <table class="widefat">
            <tr><td><strong>PHP Version:</strong></td><td><?php echo PHP_VERSION; ?></td></tr>
            <tr><td><strong>WordPress Version:</strong></td><td><?php echo get_bloginfo('version'); ?></td></tr>
            <tr><td><strong>WP_DEBUG:</strong></td><td><?php echo defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled'; ?></td></tr>
            <tr><td><strong>WP_DEBUG_LOG:</strong></td><td><?php echo defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled'; ?></td></tr>
        </table>

        <h2>Plugin Status</h2>
        <?php
        $plugin_file = WP_PLUGIN_DIR . '/whatsapp-order-button/whatsapp-order-button.php';
        $plugin_active = is_plugin_active('whatsapp-order-button/whatsapp-order-button.php');
        ?>
        <table class="widefat">
            <tr><td><strong>Plugin File Exists:</strong></td><td><?php echo file_exists($plugin_file) ? '✓ Yes' : '✗ No'; ?></td></tr>
            <tr><td><strong>Plugin Active:</strong></td><td><?php echo $plugin_active ? '✓ Yes' : '✗ No'; ?></td></tr>
            <tr><td><strong>WooCommerce Active:</strong></td><td><?php echo class_exists('WooCommerce') ? '✓ Yes' : '✗ No'; ?></td></tr>
        </table>

        <h2>File Check</h2>
        <?php
        $plugin_dir = WP_PLUGIN_DIR . '/whatsapp-order-button/';
        $files_to_check = [
            'whatsapp-order-button.php',
            'includes/class-whatsapp-order-button.php',
            'includes/class-whatsapp-admin.php',
            'includes/class-whatsapp-frontend.php',
            'admin/admin-settings.php',
            'public/css/whatsapp-order.css',
            'public/js/whatsapp-order.js'
        ];
        ?>
        <table class="widefat">
            <?php foreach ($files_to_check as $file): ?>
            <tr>
                <td><strong><?php echo $file; ?>:</strong></td>
                <td><?php echo file_exists($plugin_dir . $file) ? '✓ Exists' : '✗ Missing'; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>

        <h2>Error Log Check</h2>
        <?php
        $error_log = ini_get('error_log');
        if ($error_log && file_exists($error_log)) {
            $log_content = file_get_contents($error_log);
            $whatsapp_errors = [];
            $lines = explode("\n", $log_content);
            foreach ($lines as $line) {
                if (stripos($line, 'whatsapp') !== false || stripos($line, 'fatal') !== false) {
                    $whatsapp_errors[] = $line;
                }
            }
            
            if (!empty($whatsapp_errors)) {
                echo '<p><strong>Recent errors related to WhatsApp or Fatal errors:</strong></p>';
                echo '<textarea style="width:100%;height:200px;" readonly>';
                echo esc_textarea(implode("\n", array_slice($whatsapp_errors, -10)));
                echo '</textarea>';
            } else {
                echo '<p>No WhatsApp-related errors found in error log.</p>';
            }
        } else {
            echo '<p>Error log not accessible or not configured.</p>';
        }
        ?>

        <h2>Plugin Test</h2>
        <p>Click the button below to test if the plugin can be loaded:</p>
        <form method="post">
            <input type="hidden" name="test_plugin" value="1">
            <?php wp_nonce_field('test_plugin', 'test_nonce'); ?>
            <input type="submit" class="button" value="Test Plugin Loading">
        </form>

        <?php
        if (isset($_POST['test_plugin']) && wp_verify_nonce($_POST['test_nonce'], 'test_plugin')) {
            echo '<h3>Plugin Test Results:</h3>';
            
            try {
                if (file_exists($plugin_file)) {
                    // Try to include the plugin file
                    ob_start();
                    include_once $plugin_file;
                    $output = ob_get_clean();
                    
                    echo '<p style="color: green;">✓ Plugin file loaded successfully!</p>';
                    if (!empty($output)) {
                        echo '<p><strong>Output:</strong></p>';
                        echo '<pre>' . esc_html($output) . '</pre>';
                    }
                } else {
                    echo '<p style="color: red;">✗ Plugin file not found!</p>';
                }
            } catch (Exception $e) {
                echo '<p style="color: red;">✗ Error loading plugin: ' . esc_html($e->getMessage()) . '</p>';
            } catch (ParseError $e) {
                echo '<p style="color: red;">✗ Parse error in plugin: ' . esc_html($e->getMessage()) . '</p>';
                echo '<p>File: ' . esc_html($e->getFile()) . '</p>';
                echo '<p>Line: ' . esc_html($e->getLine()) . '</p>';
            } catch (Error $e) {
                echo '<p style="color: red;">✗ Fatal error in plugin: ' . esc_html($e->getMessage()) . '</p>';
                echo '<p>File: ' . esc_html($e->getFile()) . '</p>';
                echo '<p>Line: ' . esc_html($e->getLine()) . '</p>';
            }
        }
        ?>
    </div>
    <?php
}
