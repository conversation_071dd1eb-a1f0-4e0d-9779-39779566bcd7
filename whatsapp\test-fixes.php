<?php
/**
 * Test file to validate WhatsApp Order Button fixes
 * 
 * This file tests the fixes applied to the plugin:
 * 1. Image upload functionality
 * 2. Shop page button display
 * 3. Admin hook registration
 * 4. Error handling
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test class for WhatsApp Order Button fixes
 */
class WhatsApp_Order_Button_Test {
    
    public function __construct() {
        add_action('init', array($this, 'run_tests'));
    }
    
    /**
     * Run all tests
     */
    public function run_tests() {
        if (!current_user_can('manage_options')) {
            return;
        }
        
        if (isset($_GET['whatsapp_test']) && $_GET['whatsapp_test'] === '1') {
            $this->test_plugin_initialization();
            $this->test_admin_hooks();
            $this->test_frontend_hooks();
            $this->test_error_handling();
            $this->display_results();
        }
    }
    
    /**
     * Test plugin initialization
     */
    private function test_plugin_initialization() {
        echo "<h3>Testing Plugin Initialization</h3>";
        
        // Test if main plugin class exists
        if (class_exists('WhatsApp_Order_Button')) {
            echo "✓ Main plugin class exists<br>";
            
            $plugin = WhatsApp_Order_Button::get_instance();
            if ($plugin) {
                echo "✓ Plugin instance created successfully<br>";
            } else {
                echo "✗ Failed to create plugin instance<br>";
            }
        } else {
            echo "✗ Main plugin class not found<br>";
        }
        
        // Test WooCommerce dependency
        if (class_exists('WooCommerce')) {
            echo "✓ WooCommerce is active<br>";
        } else {
            echo "⚠ WooCommerce is not active<br>";
        }
    }
    
    /**
     * Test admin hooks
     */
    private function test_admin_hooks() {
        echo "<h3>Testing Admin Hooks</h3>";
        
        if (class_exists('WhatsApp_Order_Button_Admin')) {
            echo "✓ Admin class exists<br>";
            
            $admin = WhatsApp_Order_Button_Admin::get_instance();
            if ($admin) {
                echo "✓ Admin instance created successfully<br>";
            } else {
                echo "✗ Failed to create admin instance<br>";
            }
        } else {
            echo "✗ Admin class not found<br>";
        }
        
        // Test if admin menu hooks are registered
        if (has_action('admin_menu')) {
            echo "✓ Admin menu hooks are registered<br>";
        } else {
            echo "✗ Admin menu hooks not found<br>";
        }
        
        // Test if admin scripts are enqueued
        if (has_action('admin_enqueue_scripts')) {
            echo "✓ Admin script hooks are registered<br>";
        } else {
            echo "✗ Admin script hooks not found<br>";
        }
    }
    
    /**
     * Test frontend hooks
     */
    private function test_frontend_hooks() {
        echo "<h3>Testing Frontend Hooks</h3>";
        
        if (class_exists('WhatsApp_Order_Button_Frontend')) {
            echo "✓ Frontend class exists<br>";
            
            $frontend = WhatsApp_Order_Button_Frontend::get_instance();
            if ($frontend) {
                echo "✓ Frontend instance created successfully<br>";
            } else {
                echo "✗ Failed to create frontend instance<br>";
            }
        } else {
            echo "✗ Frontend class not found<br>";
        }
        
        // Test if shop button hooks are registered
        if (has_action('woocommerce_after_shop_loop_item')) {
            echo "✓ Shop button hooks are registered<br>";
        } else {
            echo "✗ Shop button hooks not found<br>";
        }
        
        // Test if product button hooks are registered
        if (has_action('woocommerce_single_product_summary')) {
            echo "✓ Product button hooks are registered<br>";
        } else {
            echo "✗ Product button hooks not found<br>";
        }
    }
    
    /**
     * Test error handling
     */
    private function test_error_handling() {
        echo "<h3>Testing Error Handling</h3>";
        
        // Test if error logging is working
        if (defined('WP_DEBUG') && WP_DEBUG) {
            echo "✓ WordPress debug mode is enabled<br>";
        } else {
            echo "⚠ WordPress debug mode is disabled<br>";
        }
        
        // Test if plugin has error handling in place
        $plugin = WhatsApp_Order_Button::get_instance();
        if ($plugin && method_exists($plugin, 'woocommerce_missing_notice')) {
            echo "✓ WooCommerce missing notice method exists<br>";
        } else {
            echo "✗ WooCommerce missing notice method not found<br>";
        }
    }
    
    /**
     * Display test results
     */
    private function display_results() {
        echo "<h3>Test Summary</h3>";
        echo "<p>Tests completed. Check the results above for any issues.</p>";
        echo "<p><strong>Next Steps:</strong></p>";
        echo "<ul>";
        echo "<li>1. Test image upload functionality in admin panel</li>";
        echo "<li>2. Test shop page button display</li>";
        echo "<li>3. Test settings save functionality</li>";
        echo "<li>4. Check error logs for any issues</li>";
        echo "</ul>";
    }
}

// Initialize test class if in admin and test parameter is set
if (is_admin() && isset($_GET['whatsapp_test'])) {
    new WhatsApp_Order_Button_Test();
}

/**
 * Add test link to admin menu for easy access
 */
add_action('admin_menu', function() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'tools.php',
            'WhatsApp Button Test',
            'WhatsApp Button Test',
            'manage_options',
            'whatsapp-test',
            function() {
                echo '<div class="wrap">';
                echo '<h1>WhatsApp Order Button - Test Fixes</h1>';
                echo '<p>This page tests the fixes applied to the WhatsApp Order Button plugin.</p>';
                echo '<p><a href="' . admin_url('tools.php?page=whatsapp-test&whatsapp_test=1') . '" class="button button-primary">Run Tests</a></p>';
                
                if (isset($_GET['whatsapp_test']) && $_GET['whatsapp_test'] === '1') {
                    echo '<div style="background: #fff; padding: 20px; border: 1px solid #ccc; margin-top: 20px;">';
                    // Tests will be displayed here
                    echo '</div>';
                }
                
                echo '</div>';
            }
        );
    }
});
