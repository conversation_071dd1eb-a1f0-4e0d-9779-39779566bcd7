<?php
/**
 * Advanced Analytics and Automation
 */

class WhatsApp_Advanced_Analytics {
    
    private $plugin;

    public function __construct($plugin) {
        $this->plugin = $plugin;
        $this->init_hooks();
    }

    private function init_hooks() {
        add_action('wp_ajax_whatsapp_analytics_data', array($this, 'get_analytics_data'));
        add_action('whatsapp_order_click', array($this, 'track_advanced_click'), 10, 3);
        add_action('wp_footer', array($this, 'add_heatmap_tracking'));
    }

    /**
     * Track advanced click data
     */
    public function track_advanced_click($product_id, $context, $user_data) {
        $analytics = get_option('whatsapp_advanced_analytics', array());
        $today = date('Y-m-d');
        
        if (!isset($analytics[$today])) {
            $analytics[$today] = array();
        }

        $analytics[$today][] = array(
            'product_id' => $product_id,
            'context' => $context,
            'timestamp' => current_time('timestamp'),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'ip_address' => $this->get_user_ip(),
            'referrer' => $_SERVER['HTTP_REFERER'] ?? '',
            'device_type' => wp_is_mobile() ? 'mobile' : 'desktop'
        );

        update_option('whatsapp_advanced_analytics', $analytics);
    }

    /**
     * Get analytics dashboard data
     */
    public function get_analytics_data() {
        check_ajax_referer('whatsapp_order_nonce', 'nonce');

        $period = sanitize_text_field($_POST['period'] ?? '7days');
        $analytics = $this->process_analytics_data($period);

        wp_send_json_success($analytics);
    }

    /**
     * Process analytics data for dashboard
     */
    private function process_analytics_data($period) {
        $analytics = get_option('whatsapp_advanced_analytics', array());
        $processed = array(
            'total_clicks' => 0,
            'conversion_rate' => 0,
            'top_products' => array(),
            'device_breakdown' => array('mobile' => 0, 'desktop' => 0),
            'hourly_distribution' => array(),
            'geographic_data' => array()
        );

        // Process data based on period
        $start_date = $this->get_period_start_date($period);
        
        foreach ($analytics as $date => $day_data) {
            if (strtotime($date) >= strtotime($start_date)) {
                foreach ($day_data as $click) {
                    $processed['total_clicks']++;
                    $processed['device_breakdown'][$click['device_type']]++;
                    
                    // Process hourly distribution
                    $hour = date('H', $click['timestamp']);
                    $processed['hourly_distribution'][$hour] = 
                        ($processed['hourly_distribution'][$hour] ?? 0) + 1;
                }
            }
        }

        return $processed;
    }

    /**
     * Add heatmap tracking
     */
    public function add_heatmap_tracking() {
        if ('yes' !== $this->plugin->get_setting('enable_heatmap', 'no')) {
            return;
        }

        ?>
        <script>
        (function($) {
            let heatmapData = [];
            
            $('.whatsapp-order-button').on('mouseenter', function() {
                const rect = this.getBoundingClientRect();
                heatmapData.push({
                    x: rect.left + rect.width / 2,
                    y: rect.top + rect.height / 2,
                    type: 'hover',
                    timestamp: Date.now()
                });
            });

            // Send heatmap data periodically
            setInterval(function() {
                if (heatmapData.length > 0) {
                    $.post(whatsapp_order_params.ajax_url, {
                        action: 'whatsapp_heatmap_data',
                        data: heatmapData,
                        nonce: whatsapp_order_params.nonce
                    });
                    heatmapData = [];
                }
            }, 30000);
        })(jQuery);
        </script>
        <?php
    }

    private function get_user_ip() {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
    }

    private function get_period_start_date($period) {
        switch ($period) {
            case '24hours': return date('Y-m-d', strtotime('-1 day'));
            case '7days': return date('Y-m-d', strtotime('-7 days'));
            case '30days': return date('Y-m-d', strtotime('-30 days'));
            default: return date('Y-m-d', strtotime('-7 days'));
        }
    }
}