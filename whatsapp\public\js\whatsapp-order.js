/**
 * WhatsApp Order Button JavaScript
 */

(function($) {
    'use strict';

    class WhatsAppOrderButton {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.initVariableProducts();
            this.initFloatingButton();
        }

        bindEvents() {
            // Main button click handler - handle all WhatsApp button variations
            $(document).on('click', '.whatsapp-order-button, .whatsapp-order-button-checkout', this.handleButtonClick.bind(this));

            // Size guide click handler
            $(document).on('click', '.whatsapp-size-guide-link', this.handleSizeGuideClick.bind(this));

            // Variable product change handler
            $(document).on('change', '.variations select', this.handleVariationChange.bind(this));

            // Quantity change handler
            $(document).on('change', 'input[name="quantity"]', this.handleQuantityChange.bind(this));
        }

        handleButtonClick(e) {
            e.preventDefault();

            const $button = $(e.currentTarget);
            const productId = $button.data('product-id') || 0;
            const whatsappNumber = $button.data('whatsapp-number');
            const context = $button.data('context') || 'unknown';

            // Debug logging
            if (typeof console !== 'undefined' && console.log) {
                console.log('WhatsApp button clicked:', {
                    productId: productId,
                    whatsappNumber: whatsappNumber,
                    context: context,
                    analyticsEnabled: whatsapp_order_params.enable_analytics
                });
            }

            // Validate WhatsApp number
            if (!whatsappNumber) {
                this.showError(whatsapp_order_params.messages.no_number || 'WhatsApp number not configured.');
                return;
            }

            // Handle variable products (only for product pages)
            if ($button.hasClass('whatsapp-variable-product')) {
                if (!this.validateVariableProduct()) {
                    this.showError(whatsapp_order_params.messages.select_options);
                    return;
                }
            }

            // Validate quantity (only for product pages)
            if (context === 'product' && !this.validateQuantity()) {
                this.showError(whatsapp_order_params.messages.quantity_required);
                return;
            }

            // For cart/checkout buttons, use the href directly
            if (context === 'cart' || context === 'cart-checkout' || context === 'floating') {
                const href = $button.attr('href');
                if (href && href.includes('whatsapp.com') || href.includes('whatsapp://')) {
                    // Track click if analytics enabled
                    if (whatsapp_order_params.enable_analytics === 'yes') {
                        this.trackClick(productId, context);
                    }

                    // Open WhatsApp using the existing href
                    this.openWhatsApp(href);
                    return;
                }
            }

            // Generate message and URL for product buttons
            const message = this.generateMessage(productId);
            const whatsappUrl = this.generateWhatsAppUrl(whatsappNumber, message);

            // Track click if analytics enabled
            if (whatsapp_order_params.enable_analytics === 'yes') {
                this.trackClick(productId, context);
            }

            // Open WhatsApp
            this.openWhatsApp(whatsappUrl);
        }

        handleSizeGuideClick(e) {
            e.preventDefault();
            
            const productId = $(e.currentTarget).data('product-id');
            
            // You can implement a modal or redirect to size guide page
            // For now, we'll show a simple alert
            alert('Size guide functionality can be customized here.');
        }

        handleVariationChange() {
            this.updateVariableProductButton();
        }

        handleQuantityChange() {
            this.updateVariableProductButton();
        }

        initVariableProducts() {
            this.updateVariableProductButton();
        }

        initFloatingButton() {
            // Add animation to floating button
            $('.whatsapp-floating-button').each(function() {
                $(this).css('animation', 'whatsapp-float 3s ease-in-out infinite');
            });
        }

        validateVariableProduct() {
            const $form = $('.variations_form');
            if ($form.length === 0) return true;

            const $selects = $form.find('select');
            let allSelected = true;

            $selects.each(function() {
                if ($(this).val() === '') {
                    allSelected = false;
                    return false;
                }
            });

            return allSelected;
        }

        validateQuantity() {
            const $quantityInput = $('input[name="quantity"]');
            if ($quantityInput.length === 0) return true;

            const quantity = parseInt($quantityInput.val());
            return quantity > 0;
        }

        updateVariableProductButton() {
            const $button = $('.whatsapp-variable-product');
            if ($button.length === 0) return;

            if (this.validateVariableProduct()) {
                $button.addClass('enabled').removeClass('disabled');
            } else {
                $button.removeClass('enabled').addClass('disabled');
            }
        }

        generateMessage(productId) {
            const productName = this.getProductName();
            const price = this.getProductPrice();
            const quantity = this.getQuantity();
            const variations = this.getVariations();
            const productUrl = window.location.href;

            // Default message template
            let message = `Hi! I would like to order:

Product: ${productName}
Price: ${price}
Quantity: ${quantity}`;

            if (variations) {
                message += `\n${variations}`;
            }

            message += `\n\nProduct Link: ${productUrl}`;

            return message;
        }

        getProductName() {
            const $title = $('.product_title, .entry-title, h1.product-title');
            return $title.length ? $title.first().text().trim() : 'Product';
        }

        getProductPrice() {
            const $price = $('.price .woocommerce-Price-amount, .price ins .woocommerce-Price-amount, .price .amount');
            return $price.length ? $price.first().text().trim() : '';
        }

        getQuantity() {
            const $quantity = $('input[name="quantity"]');
            return $quantity.length ? $quantity.val() : '1';
        }

        getVariations() {
            const $form = $('.variations_form');
            if ($form.length === 0) return '';

            let variations = '';
            const $selects = $form.find('select');

            $selects.each(function() {
                const $select = $(this);
                const label = $select.closest('tr').find('label').text().replace(':', '').trim();
                const value = $select.find('option:selected').text();

                if (value && value !== 'Choose an option') {
                    variations += `${label}: ${value}\n`;
                }
            });

            return variations.trim();
        }

        generateWhatsAppUrl(number, message) {
            const encodedMessage = encodeURIComponent(message);
            const cleanNumber = number.replace(/[^0-9]/g, '');

            // Determine if mobile or desktop
            const isMobile = whatsapp_order_params.is_mobile || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const deviceBehavior = whatsapp_order_params.device_behavior;

            if ((isMobile && deviceBehavior !== 'web') || deviceBehavior === 'app') {
                return `whatsapp://send?phone=${cleanNumber}&text=${encodedMessage}`;
            } else {
                return `https://web.whatsapp.com/send?phone=${cleanNumber}&text=${encodedMessage}`;
            }
        }

        openWhatsApp(url) {
            // Try to open in new tab/window
            const newWindow = window.open(url, '_blank');
            
            // Fallback for mobile devices
            if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                window.location.href = url;
            }
        }

        trackClick(productId, context) {
            // Debug logging
            if (typeof console !== 'undefined' && console.log) {
                console.log('Tracking WhatsApp click:', {
                    productId: productId,
                    context: context,
                    ajaxUrl: whatsapp_order_params.ajax_url,
                    nonce: whatsapp_order_params.nonce
                });
            }

            $.ajax({
                url: whatsapp_order_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'whatsapp_order_track',
                    product_id: productId,
                    context: context,
                    nonce: whatsapp_order_params.nonce
                },
                success: function(response) {
                    if (typeof console !== 'undefined' && console.log) {
                        console.log('Analytics tracking successful:', response);
                    }

                    // Track with Google Analytics if available
                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'whatsapp_order_click', {
                            'event_category': 'WhatsApp Order',
                            'event_label': context,
                            'value': productId
                        });
                    }

                    // Track with Google Analytics Universal if available
                    if (typeof ga !== 'undefined') {
                        ga('send', 'event', 'WhatsApp Order', 'Click', context, productId);
                    }
                },
                error: function(xhr, status, error) {
                    if (typeof console !== 'undefined' && console.error) {
                        console.error('Analytics tracking failed:', {
                            status: status,
                            error: error,
                            response: xhr.responseText
                        });
                    }
                }
            });
        }

        showError(message) {
            // Remove existing error messages
            $('.whatsapp-order-error').remove();

            // Create error message
            const $error = $('<div class="whatsapp-order-error">' + message + '</div>');
            
            // Find the best place to show the error
            const $target = $('.whatsapp-order-button-wrapper').first();
            if ($target.length) {
                $target.before($error);
            } else {
                $('body').prepend($error);
            }

            // Auto-hide after 5 seconds
            setTimeout(function() {
                $error.fadeOut(function() {
                    $(this).remove();
                });
            }, 5000);
        }

        showSuccess(message) {
            // Remove existing success messages
            $('.whatsapp-order-success').remove();

            // Create success message
            const $success = $('<div class="whatsapp-order-success">' + message + '</div>');
            
            // Find the best place to show the success
            const $target = $('.whatsapp-order-button-wrapper').first();
            if ($target.length) {
                $target.before($success);
            } else {
                $('body').prepend($success);
            }

            // Auto-hide after 3 seconds
            setTimeout(function() {
                $success.fadeOut(function() {
                    $(this).remove();
                });
            }, 3000);
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        new WhatsAppOrderButton();
    });

    // Add floating animation CSS
    const floatingCSS = `
        @keyframes whatsapp-float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    `;

    $('<style>').prop('type', 'text/css').html(floatingCSS).appendTo('head');

    // Mobile-friendly floating button behavior
    if (window.innerWidth <= 768) {
        let scrollTimeout;
        let isScrolling = false;

        // Hide button while scrolling on mobile
        $(window).on('scroll', function() {
            const $floatingButton = $('.whatsapp-floating-button');

            if (!isScrolling) {
                isScrolling = true;
                $floatingButton.css('opacity', '0.5');
            }

            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(function() {
                isScrolling = false;
                $floatingButton.css('opacity', '1');
            }, 150);
        });

        // Detect mobile menu toggles (common patterns)
        $('body').on('click', '.menu-toggle, .mobile-menu-toggle, .hamburger, [data-toggle="mobile-menu"]', function() {
            const $floatingButton = $('.whatsapp-floating-button');
            setTimeout(function() {
                if ($('body').hasClass('menu-open') || $('body').hasClass('mobile-menu-open') || $('.mobile-menu').is(':visible')) {
                    $floatingButton.addClass('menu-active-hide');
                } else {
                    $floatingButton.removeClass('menu-active-hide');
                }
            }, 100);
        });
    }

})(jQuery);
