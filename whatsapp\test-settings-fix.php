<?php
/**
 * Test script to verify settings fix
 * Run this from WordPress admin or via WP-CLI
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h2>WhatsApp Order Button - Settings Fix Test</h2>\n";

// Test 1: Check if main plugin class exists
echo "<h3>Test 1: Plugin Class Check</h3>\n";
if (class_exists('WhatsApp_Order_Button')) {
    echo "✅ WhatsApp_Order_Button class exists\n";
    $plugin = WhatsApp_Order_Button::get_instance();
    if ($plugin) {
        echo "✅ Plugin instance created successfully\n";
    } else {
        echo "❌ Failed to create plugin instance\n";
    }
} else {
    echo "❌ WhatsApp_Order_Button class not found\n";
}

// Test 2: Check default settings
echo "<h3>Test 2: Default Settings Check</h3>\n";
if (isset($plugin)) {
    $settings = $plugin->get_settings();
    
    // Check for the previously missing settings
    $required_settings = array(
        'button_style' => 'modern',
        'custom_button_image' => '',
        'show_on_shop' => 'no',
        'enable_multiple_numbers' => 'no',
        'phone_numbers' => array(),
        'multiple_numbers_style' => 'dropdown'
    );
    
    foreach ($required_settings as $key => $expected_default) {
        if (array_key_exists($key, $settings)) {
            echo "✅ Setting '{$key}' exists with value: " . (is_array($settings[$key]) ? 'array(' . count($settings[$key]) . ')' : $settings[$key]) . "\n";
        } else {
            echo "❌ Setting '{$key}' is missing\n";
        }
    }
} else {
    echo "❌ Cannot test settings - plugin instance not available\n";
}

// Test 3: Check admin class
echo "<h3>Test 3: Admin Class Check</h3>\n";
if (class_exists('WhatsApp_Order_Button_Admin')) {
    echo "✅ WhatsApp_Order_Button_Admin class exists\n";
    
    // Check if the sanitize_phone_numbers method exists
    $admin_class = new ReflectionClass('WhatsApp_Order_Button_Admin');
    if ($admin_class->hasMethod('sanitize_phone_numbers')) {
        echo "✅ sanitize_phone_numbers method exists\n";
    } else {
        echo "❌ sanitize_phone_numbers method missing\n";
    }
} else {
    echo "❌ WhatsApp_Order_Button_Admin class not found\n";
}

// Test 4: Check frontend class
echo "<h3>Test 4: Frontend Class Check</h3>\n";
if (class_exists('WhatsApp_Order_Button_Frontend')) {
    echo "✅ WhatsApp_Order_Button_Frontend class exists\n";
    
    // Check if the new methods exist
    $frontend_class = new ReflectionClass('WhatsApp_Order_Button_Frontend');
    $new_methods = array('generate_multiple_numbers_button', 'generate_dropdown_selector');
    
    foreach ($new_methods as $method) {
        if ($frontend_class->hasMethod($method)) {
            echo "✅ {$method} method exists\n";
        } else {
            echo "❌ {$method} method missing\n";
        }
    }
} else {
    echo "❌ WhatsApp_Order_Button_Frontend class not found\n";
}

// Test 5: Simulate settings save
echo "<h3>Test 5: Settings Save Simulation</h3>\n";
if (isset($plugin)) {
    $test_settings = array(
        'enabled' => 'yes',
        'whatsapp_number' => '+1234567890',
        'button_text' => 'Test Order Button',
        'button_style' => 'classic',
        'custom_button_image' => 'https://example.com/button.png',
        'show_on_shop' => 'yes',
        'enable_multiple_numbers' => 'yes',
        'phone_numbers' => array(
            array('label' => 'Sales', 'number' => '+1111111111', 'default' => true),
            array('label' => 'Support', 'number' => '+2222222222', 'default' => false)
        ),
        'multiple_numbers_style' => 'buttons'
    );
    
    try {
        $plugin->update_settings($test_settings);
        echo "✅ Settings update method executed without errors\n";
        
        // Verify settings were saved
        $saved_settings = $plugin->get_settings();
        $test_keys = array('button_style', 'custom_button_image', 'show_on_shop', 'enable_multiple_numbers');
        
        foreach ($test_keys as $key) {
            if (isset($saved_settings[$key]) && $saved_settings[$key] === $test_settings[$key]) {
                echo "✅ Setting '{$key}' saved correctly: {$saved_settings[$key]}\n";
            } else {
                echo "❌ Setting '{$key}' not saved correctly\n";
            }
        }
        
        // Test phone numbers array
        if (isset($saved_settings['phone_numbers']) && is_array($saved_settings['phone_numbers']) && count($saved_settings['phone_numbers']) === 2) {
            echo "✅ Phone numbers array saved correctly with " . count($saved_settings['phone_numbers']) . " entries\n";
        } else {
            echo "❌ Phone numbers array not saved correctly\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Settings save failed with error: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Cannot test settings save - plugin instance not available\n";
}

echo "<h3>Test Summary</h3>\n";
echo "If all tests show ✅, the settings fix is working correctly!\n";
echo "If any tests show ❌, there may be issues that need to be addressed.\n";
