<?php
/**
 * WCFM Multi-vendor Connector
 */

class WhatsApp_WCFM_Connector {
    
    private $plugin;

    public function __construct($plugin) {
        $this->plugin = $plugin;
        
        if ($this->is_wcfm_active()) {
            $this->init_hooks();
        }
    }

    private function is_wcfm_active() {
        return class_exists('WCFM');
    }

    private function init_hooks() {
        add_action('wcfm_products_manage_form_load_views', array($this, 'add_wcfm_fields'));
        add_action('wcfm_product_manage_form_basic_fields', array($this, 'add_basic_fields'), 10, 3);
        add_filter('wcfm_product_manage_fields_general', array($this, 'add_general_fields'));
        add_action('wcfm_product_manage_form_update_data', array($this, 'save_vendor_settings'), 10, 2);
    }

    /**
     * Add WhatsApp fields to WCFM product form
     */
    public function add_wcfm_fields() {
        global $WCFM, $WCFMu;
        ?>
        <div class="wcfm_clearfix"></div>
        <h2><?php _e('WhatsApp Settings', 'whatsapp-order-button'); ?></h2>
        <div class="wcfm_clearfix"></div>
        <?php
    }

    /**
     * Add basic WhatsApp fields
     */
    public function add_basic_fields($product_id, $product_type, $term_id) {
        global $WCFM;
        
        $vendor_whatsapp = get_post_meta($product_id, '_vendor_whatsapp_number', true);
        $disable_global = get_post_meta($product_id, '_disable_global_whatsapp', true);
        
        $WCFM->wcfm_fields->wcfm_generate_form_field(array(
            'vendor_whatsapp_number' => array(
                'label' => __('Vendor WhatsApp Number', 'whatsapp-order-button'),
                'type' => 'text',
                'class' => 'wcfm-text wcfm_ele',
                'label_class' => 'wcfm_title',
                'value' => $vendor_whatsapp,
                'hints' => __('Enter your WhatsApp number with country code (e.g., +1234567890)', 'whatsapp-order-button')
            ),
            'disable_global_whatsapp' => array(
                'label' => __('Use Vendor WhatsApp Only', 'whatsapp-order-button'),
                'type' => 'checkbox',
                'class' => 'wcfm-checkbox wcfm_ele',
                'label_class' => 'wcfm_title checkbox_title',
                'value' => 'yes',
                'dfvalue' => $disable_global,
                'hints' => __('Check to use only vendor WhatsApp number for this product', 'whatsapp-order-button')
            )
        ));
    }

    /**
     * Save vendor WhatsApp settings
     */
    public function save_vendor_settings($product_id, $wcfm_products_manage_form_data) {
        if (isset($wcfm_products_manage_form_data['vendor_whatsapp_number'])) {
            update_post_meta($product_id, '_vendor_whatsapp_number', 
                sanitize_text_field($wcfm_products_manage_form_data['vendor_whatsapp_number']));
        }

        if (isset($wcfm_products_manage_form_data['disable_global_whatsapp'])) {
            update_post_meta($product_id, '_disable_global_whatsapp', 'yes');
        } else {
            delete_post_meta($product_id, '_disable_global_whatsapp');
        }
    }

    /**
     * Get vendor WhatsApp number for product
     */
    public function get_vendor_whatsapp($product_id) {
        $vendor_whatsapp = get_post_meta($product_id, '_vendor_whatsapp_number', true);
        
        if (!empty($vendor_whatsapp)) {
            return $vendor_whatsapp;
        }

        // Fallback to vendor's store WhatsApp
        $vendor_id = get_post_field('post_author', $product_id);
        return get_user_meta($vendor_id, '_vendor_whatsapp_number', true);
    }
}