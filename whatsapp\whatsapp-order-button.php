<?php
/**
 * Plugin Name: WhatsApp Order Button for WooCommerce - Clothing Store Edition
 * Plugin URI: https://yourwebsite.com/whatsapp-order-button
 * Description: Replace or complement WooCommerce cart/checkout with WhatsApp ordering system, tailored for clothing stores.
 * Version: 1.0.0
 * Author: hariom giri
 * Author URI: https://yourwebsite.com
 * Text Domain: whatsapp-order-button
 * Domain Path: /languages
 * Requires at least: 5.8
 * Tested up to: 6.4
 * Requires PHP: 7.0
 * WC requires at least: 6.0
 * WC tested up to: 8.5
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check PHP version
if (version_compare(PHP_VERSION, '7.0', '<')) {
    add_action('admin_notices', function() {
        echo '<div class="notice notice-error"><p>';
        echo sprintf(
            __('WhatsApp Order Button requires PHP version 7.0 or higher. You are running PHP %s. Please upgrade your PHP version.', 'whatsapp-order-button'),
            PHP_VERSION
        );
        echo '</p></div>';
    });
    return;
}

// Enable error logging for debugging
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('WhatsApp Order Button: Plugin file loaded, PHP version: ' . PHP_VERSION);
}

// Define plugin constants
define('WHATSAPP_ORDER_BUTTON_VERSION', '1.0.0');
define('WHATSAPP_ORDER_BUTTON_PLUGIN_FILE', __FILE__);
define('WHATSAPP_ORDER_BUTTON_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WHATSAPP_ORDER_BUTTON_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WHATSAPP_ORDER_BUTTON_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Check if WooCommerce is active
 */
function whatsapp_order_button_check_woocommerce() {
    if (!class_exists('WooCommerce')) {
        add_action('admin_notices', 'whatsapp_order_button_woocommerce_notice');
        deactivate_plugins(plugin_basename(__FILE__));
        return false;
    }
    return true;
}

/**
 * Display notice if WooCommerce is not active
 */
function whatsapp_order_button_woocommerce_notice() {
    ?>
    <div class="notice notice-error">
        <p><?php _e('WhatsApp Order Button requires WooCommerce to be installed and active.', 'whatsapp-order-button'); ?></p>
    </div>
    <?php
}

/**
 * Initialize the plugin
 */
function whatsapp_order_button_init() {
    if (!whatsapp_order_button_check_woocommerce()) {
        return;
    }

    // Load plugin text domain
    load_plugin_textdomain('whatsapp-order-button', false, dirname(plugin_basename(__FILE__)) . '/languages');

    // Include required files with error handling
    $main_class_file = WHATSAPP_ORDER_BUTTON_PLUGIN_DIR . 'includes/class-whatsapp-order-button.php';

    if (!file_exists($main_class_file)) {
        add_action('admin_notices', function() use ($main_class_file) {
            echo '<div class="notice notice-error"><p>';
            echo sprintf(__('WhatsApp Order Button: Main class file not found at %s', 'whatsapp-order-button'), $main_class_file);
            echo '</p></div>';
        });
        return;
    }

    require_once $main_class_file;

    // Initialize the main plugin class with error handling
    if (class_exists('WhatsApp_Order_Button')) {
        try {
            WhatsApp_Order_Button::get_instance();
        } catch (Exception $e) {
            add_action('admin_notices', function() use ($e) {
                echo '<div class="notice notice-error"><p>';
                echo sprintf(__('WhatsApp Order Button initialization error: %s', 'whatsapp-order-button'), $e->getMessage());
                echo '</p></div>';
            });
        }
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>';
            echo __('WhatsApp Order Button: Main class not found after including file.', 'whatsapp-order-button');
            echo '</p></div>';
        });
    }
}

// Hook into plugins_loaded to ensure WooCommerce is loaded first
add_action('plugins_loaded', 'whatsapp_order_button_init');

/**
 * Plugin activation hook
 */
function whatsapp_order_button_activate() {
    if (!whatsapp_order_button_check_woocommerce()) {
        wp_die(__('WhatsApp Order Button requires WooCommerce to be installed and active.', 'whatsapp-order-button'));
    }

    // Set default options
    $default_options = array(
        'enabled' => 'yes',
        'whatsapp_number' => '',
        'button_text' => __('Order on WhatsApp', 'whatsapp-order-button'),
        'button_position' => 'after_add_to_cart',
        'disable_add_to_cart' => 'no',
        'message_template' => __('Hi! I would like to order:\n\nProduct: {product_name}\nPrice: {price}\nQuantity: {quantity}\n{variations}\n\nProduct Link: {product_url}', 'whatsapp-order-button'),
        'button_color' => '#25D366',
        'button_text_color' => '#ffffff',
        'button_size' => 'medium',
        'button_style' => 'modern',
        'custom_button_image' => '',
        'show_on_shop' => 'no',
        'show_on_cart' => 'yes',
        'show_on_checkout' => 'yes',
        'replace_checkout_button' => 'no',
        'device_behavior' => 'auto',
        'enable_analytics' => 'no',
        'floating_button' => 'no',
        'floating_position' => 'bottom-right',
        'show_size_guide' => 'no',
        'size_guide_text' => __('Size Guide', 'whatsapp-order-button'),
        'custom_css' => '',
        'exclude_categories' => array(),
        'exclude_products' => array(),
        // Multiple numbers settings
        'enable_multiple_numbers' => 'no',
        'phone_numbers' => array(),
        'default_phone_number' => 0,
        'multiple_numbers_style' => 'dropdown',
        'multiple_numbers_text' => __('Choose Department', 'whatsapp-order-button'),
        // New URL customization settings
        'enable_custom_urls' => 'no',
        'custom_url_template' => '',
        'custom_url_fallback' => 'whatsapp'
    );

    add_option('whatsapp_order_button_settings', $default_options);
}
register_activation_hook(__FILE__, 'whatsapp_order_button_activate');

/**
 * Plugin deactivation hook
 */
function whatsapp_order_button_deactivate() {
    // Clean up if needed
}
register_deactivation_hook(__FILE__, 'whatsapp_order_button_deactivate');
