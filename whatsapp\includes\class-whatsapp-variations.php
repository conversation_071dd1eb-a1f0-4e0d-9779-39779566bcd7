<?php
/**
 * OneClick Variations Grabber
 */

class WhatsApp_Variations_Grabber {
    
    private $plugin;

    public function __construct($plugin) {
        $this->plugin = $plugin;
        $this->init_hooks();
    }

    private function init_hooks() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_whatsapp_auto_variations', array($this, 'handle_auto_variations'));
        add_action('wp_ajax_nopriv_whatsapp_auto_variations', array($this, 'handle_auto_variations'));
    }

    /**
     * Enqueue variation grabber scripts
     */
    public function enqueue_scripts() {
        if ('yes' !== $this->plugin->get_setting('enable_auto_variations', 'no')) {
            return;
        }

        wp_enqueue_script(
            'whatsapp-variations-grabber',
            plugin_dir_url(__FILE__) . '../public/js/variations-grabber.js',
            array('jquery'),
            '1.0.0',
            true
        );

        wp_localize_script('whatsapp-variations-grabber', 'whatsapp_variations_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('whatsapp_variations_nonce'),
            'auto_detect_delay' => $this->plugin->get_setting('auto_detect_delay', '2000')
        ));
    }

    /**
     * Auto-detect and grab variations
     */
    public function handle_auto_variations() {
        check_ajax_referer('whatsapp_variations_nonce', 'nonce');

        $product_id = intval($_POST['product_id']);
        $product = wc_get_product($product_id);

        if (!$product || !$product->is_type('variable')) {
            wp_send_json_error(__('Invalid variable product.', 'whatsapp-order-button'));
        }

        $variations = $this->get_smart_variations($product);
        
        wp_send_json_success(array(
            'variations' => $variations,
            'message' => __('Variations detected automatically.', 'whatsapp-order-button')
        ));
    }

    /**
     * Get smart variations based on user behavior
     */
    private function get_smart_variations($product) {
        $available_variations = $product->get_available_variations();
        $popular_combinations = $this->get_popular_combinations($product->get_id());
        
        // Return most popular or first available combination
        if (!empty($popular_combinations)) {
            return $popular_combinations[0];
        }

        return !empty($available_variations) ? $available_variations[0] : array();
    }

    /**
     * Get popular variation combinations
     */
    private function get_popular_combinations($product_id) {
        // Implementation for tracking popular combinations
        $combinations = get_option('whatsapp_popular_variations_' . $product_id, array());
        
        // Sort by popularity
        arsort($combinations);
        
        return array_keys($combinations);
    }
}