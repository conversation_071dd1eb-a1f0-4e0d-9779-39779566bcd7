=== WhatsApp Order Button for WooCommerce - Clothing Store Edition ===
Contributors: yourname
Tags: whatsapp, woocommerce, order, clothing, ecommerce, chat, mobile
Requires at least: 5.8
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Replace or complement WooCommerce cart/checkout with WhatsApp ordering system, tailored for clothing stores.

== Description ==

**WhatsApp Order Button for WooCommerce - Clothing Store Edition** is a powerful plugin designed specifically for clothing stores that want to streamline their ordering process through WhatsApp. This plugin allows customers to place orders directly via WhatsApp with complete product details, making it perfect for businesses that prefer chat-based customer interactions.

### 🚀 Key Features

**📱 WhatsApp Integration**
* Add WhatsApp order buttons on product pages, cart, and checkout
* Auto-filled messages with product details, variations, and pricing
* Smart device detection (WhatsApp app on mobile, web on desktop)
* Customizable message templates with dynamic placeholders

**👕 Clothing Store Specific**
* Support for all product variations (size, color, fit, etc.)
* Optional size guide popup integration
* Variation details automatically included in WhatsApp messages
* Perfect for fashion and apparel businesses

**⚙️ Flexible Configuration**
* Global WhatsApp number with per-product/category overrides
* Option to disable "Add to Cart" functionality completely
* Multiple button positions and display options
* Floating WhatsApp button for constant visibility

**🎨 Customization Options**
* Fully customizable button appearance (colors, sizes, text)
* Multiple button positions on product pages
* Custom CSS support for advanced styling
* Responsive design for all devices

**🌐 Multi-language & RTL Support**
* Translation-ready with .pot file included
* RTL language support (Arabic, Hebrew, etc.)
* Compatible with WPML and Polylang

**📊 Analytics & Tracking**
* Built-in click tracking
* Google Analytics integration support
* Simple analytics dashboard

### 🎯 Perfect For

* Clothing and fashion stores
* Boutiques and small retailers
* Businesses preferring personal customer service
* Mobile-first shopping experiences
* International businesses using WhatsApp

### 🔧 How It Works

1. Customer visits your product page
2. Selects size, color, and quantity
3. Clicks "Order on WhatsApp" button
4. WhatsApp opens with pre-filled product details
5. Customer completes order via chat
6. You handle payment and delivery through WhatsApp

### 💡 Use Cases

* **Replace Traditional Checkout**: Disable WooCommerce cart entirely and use WhatsApp as primary ordering method
* **Complement Existing Checkout**: Offer WhatsApp as an alternative ordering option
* **Customer Support**: Use floating button for general inquiries
* **International Sales**: Perfect for businesses selling across different countries

### 🛠️ Technical Features

* **WordPress 5.8+** and **WooCommerce 6.0+** compatible
* **PHP 7.4+** support
* HPOS (High-Performance Order Storage) compatible
* Clean, lightweight code following WordPress standards
* Mobile-first responsive design
* SEO-friendly implementation

== Installation ==

### Automatic Installation

1. Go to your WordPress admin dashboard
2. Navigate to Plugins → Add New
3. Search for "WhatsApp Order Button for WooCommerce"
4. Click "Install Now" and then "Activate"

### Manual Installation

1. Download the plugin zip file
2. Go to Plugins → Add New → Upload Plugin
3. Choose the downloaded zip file and click "Install Now"
4. Activate the plugin

### Setup

1. Go to **WooCommerce → WhatsApp Order** in your admin dashboard
2. Enter your WhatsApp number (with country code, e.g., +**********)
3. Customize button text, colors, and position
4. Configure message template with available placeholders
5. Save settings and test the functionality

== Frequently Asked Questions ==

= Do I need a WhatsApp Business account? =

No, the plugin works with regular WhatsApp numbers. However, WhatsApp Business is recommended for better customer management.

= Can I use different WhatsApp numbers for different products? =

Yes! You can set global WhatsApp number and override it per product or per category.

= Will this work on mobile devices? =

Absolutely! The plugin automatically detects mobile devices and opens the WhatsApp app, while desktop users are directed to WhatsApp Web.

= Can I disable the regular WooCommerce checkout? =

Yes, you can disable "Add to Cart" functionality globally or per product/category, forcing customers to use WhatsApp ordering.

= Is the plugin translation-ready? =

Yes, the plugin is fully translation-ready and includes RTL language support.

= Does it work with variable products? =

Yes! The plugin fully supports variable products and includes all selected variations in the WhatsApp message.

= Can I customize the WhatsApp message? =

Yes, you can customize the message template using placeholders like {product_name}, {price}, {quantity}, {variations}, and {product_url}.

= Is there analytics support? =

Yes, the plugin includes basic click tracking and supports Google Analytics integration.

== Screenshots ==

1. **Admin Settings Page** - Easy-to-use settings panel with tabbed interface
2. **Product Page Button** - WhatsApp button integrated on product pages
3. **Mobile Experience** - Optimized mobile interface with app integration
4. **Message Template** - Auto-generated WhatsApp message with product details
5. **Floating Button** - Optional floating WhatsApp button for constant visibility
6. **Cart Integration** - WhatsApp ordering option on cart page

== Changelog ==

= 1.0.0 =
* Initial release
* WhatsApp order button integration
* Product variation support
* Customizable button appearance
* Multi-language support
* Analytics tracking
* Floating button option
* Admin settings panel
* Mobile optimization

== Upgrade Notice ==

= 1.0.0 =
Initial release of WhatsApp Order Button for WooCommerce - Clothing Store Edition.

== Support ==

For support, feature requests, or bug reports, please visit our [support forum](https://wordpress.org/support/plugin/whatsapp-order-button/) or contact us through our website.

== Privacy Policy ==

This plugin does not collect or store any personal data. All WhatsApp communications happen directly between your customers and your WhatsApp number. Click tracking (if enabled) only stores anonymous usage statistics.

== Credits ==

* WhatsApp icon and branding are trademarks of Meta Platforms, Inc.
* Plugin developed following WordPress and WooCommerce coding standards
* Special thanks to the WordPress and WooCommerce communities
